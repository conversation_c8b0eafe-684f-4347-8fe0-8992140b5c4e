import Fluent
import JWTKit
import FluentPostgresDriver
import Vapor
import APNS
import QueuesRedisDriver
import Leaf
import SotoSNS
import SotoCore

let isProduction = false
let isStaging    = true
let isLocal      = false

extension String {
    static var navigatorEndpoint = isProduction ? "arn:aws:sns:us-east-1:628326973807:app/APNS/navigator" : "arn:aws:sns:us-east-1:628326973807:app/APNS_SANDBOX/navigator-stg"
}

enum PushTopic: String {
    
    static var apns = isProduction ? "APNS" : "APNS_SANDBOX"
    
    case navigatorAppTopic = "com.welluptechnologies.navigator"
    case wellupAppTopic = "com.welluptechnologies.wellup"
    
    static var  navigatorSubscriptionTopic = isProduction ? "arn:aws:sns:us-east-1:628326973807:navigatorPushProduction" : "arn:aws:sns:us-east-1:628326973807:navigatorPushStaging"
    
    
    static func topicFor(type:String?) -> PushTopic {
        guard let sendTo = type?.lowercased() else { return .navigatorAppTopic }
        return sendTo == "member" ? .wellupAppTopic : .navigatorAppTopic
    }
}

// configures your application
public func configure(_ app: Application) throws {
    
//    app.http.server.configuration.supportVersions = [.two] //.one
    app.http.server.configuration.requestDecompression = .enabled
    app.http.server.configuration.responseCompression = .enabled
    app.http.server.configuration.supportPipelining = true
    app.http.server.configuration.reuseAddress = true
    app.http.server.configuration.backlog = 256
    
    let corsConfiguration = CORSMiddleware.Configuration(
        allowedOrigin: .all,
        allowedMethods: [.GET, .POST, .PUT, .OPTIONS, .DELETE, .PATCH],
        allowedHeaders: [.accept, .authorization, .contentType, .origin, .xRequestedWith, .userAgent, .accessControlAllowOrigin]
    )
    let cors = CORSMiddleware(configuration: corsConfiguration)
    let error = ErrorMiddleware.default(environment: app.environment)
    
    // Clear any existing middleware.
    app.middleware = .init()
    app.middleware.use(cors)
    app.middleware.use(error)
    app.middleware.use(FileMiddleware(publicDirectory: app.directory.publicDirectory))
    
    app.middleware.use(AuthMiddleware())
    app.middleware.use(OrgRequestMiddleware())
    
    app.routes.defaultMaxBodySize = "50mb"
    
    //NOTE: <EMAIL>
    app.aws.client = app.aws.clientOrInit    
        
    try DatabaseConfiguration.configure(app)
    
    app.migrations.add(OrganizationMigration())
    
    app.migrations.add(UserMigration())
    app.migrations.add(MemberMigration())
    
    app.migrations.add(TaskModelMigration())
    
    app.migrations.add(NetworkMigration()) //NOTE: needed to comment out care pagacke parent then run it
    app.migrations.add(NetworkServicesMigration())
    
    
    app.migrations.add(ReasonMigration())
    
    app.migrations.add(CarePackageMigration())
    app.migrations.add(CarePackageItemMigration())
    
    app.migrations.add(ServiceMigration())
    app.migrations.add(ServiceRuleMigration())
    
    
    app.migrations.add(NoteMigration())
    app.migrations.add(TagMigration())    
    app.migrations.add(PhoneNumberMigration())
    app.migrations.add(HouseholdMigration())
    app.migrations.add(HouseholdMigrationUpdate())
    
    
    app.migrations.add(SchoolMigration())
    app.migrations.add(AnimalMigration())
    
    app.migrations.add(TeamMigration())
    
    app.migrations.add(AddressMigration())
    
    
    
    
    app.migrations.add(SurveyMigration())
    app.migrations.add(SurveyMigrationUpdate())
    
    app.migrations.add(SectionMigration())
    app.migrations.add(QuestionMigration())
    app.migrations.add(TemplateMigration())
    app.migrations.add(AnswerMigration())
    app.migrations.add(AnswerMigrationUpdate())
    app.migrations.add(AnswerAddKeyMigrationUpdate())
    
    
    app.migrations.add(AttachmentMigration())
    
    
    app.migrations.add(TimelineItemMigration())
    app.migrations.add(TimelineItemServiceMigration())
    
        
    app.migrations.add(ChatMigration())
    
    
    //Many
    app.migrations.add(UserTeamsMigration())
    app.migrations.add(UserChatsMigration())
    app.migrations.add(HouseholdTeamsMigration())
    app.migrations.add(HouseholdMembersMigration())
        
    
    //Auth
    app.migrations.add(CreateAuthUserMigration())
    app.migrations.add(CreateTokensMigration())
    app.migrations.add(AddPasswordResetToAuthUser())
    app.migrations.add(CreatePasswordResetAttemptsMigration())
    app.migrations.add(CreateShortUrlMigration())
    
    
    app.migrations.add(ConsentMigration())
    
    
    app.migrations.add(DeviceMigration())
    
    app.migrations.add(AppointmentMigration())
    
    app.migrations.add(PhoneNumberUpdateMigration())
    
    app.migrations.add(MemberMigrationUpdate())
        
    app.migrations.add(UserTasksMigration())
    app.migrations.add(MemberTasksMigration())
    app.migrations.add(TaskDetailMigration())
    app.migrations.add(OrgTaskStatusMigration())
    
    app.migrations.add(TaskModelUpdateMigration())
    app.migrations.add(AttachmentUpdateMigration())
    app.migrations.add(AttachmentUpdateTaskMigration())
    
    app.migrations.add(TaskModelUpdate2Migration())
    app.migrations.add(AddressUpdateMigration())
    app.migrations.add(OrganizationUpdateMigration())
    app.migrations.add(NotificationMigration())
    
    
    //
    
    app.migrations.add(InsurancePolicyMigration())
    app.migrations.add(CarrierMigration())
    app.migrations.add(TagUpdateMigration())
    app.migrations.add(AddressPolicyUpdateMigration())
    app.migrations.add(PhonePolicyNumberUpdateMigration())
    app.migrations.add(CreateInsuranceCard())
    app.migrations.add(AttachmentUpdateInsuranceCardMigration())
    app.migrations.add(MemberDeliveryMigrationUpdate())
    app.migrations.add(NetworkUpdateMigration())
    app.migrations.add(ItemAppointmentsMigration())
    app.migrations.add(InsurancePlanMigration())
    app.migrations.add(SurveyTaskMigrationUpdate())
    app.migrations.add(TagMemberUpdateMigration())
    app.migrations.add(MemberPregnancyStatusMigrationUpdate())
    app.migrations.add(AppointmentUpdateMigration())
    app.migrations.add(MillitaryStatusMigrationUpdate())
    app.migrations.add(TemplateUpdateMigration())
    app.migrations.add(TemplateLangUpdateMigration())
    app.migrations.add(AppointmentRateUpdateMigration())
    app.migrations.add(TaskModelUpdateTasksMigration())
    
//    app.migrations.add(AppointmentscheduleEpocUpdateMigration())
//    app.migrations.add(AppointmentMemberBookUpdateMigration())
//    app.migrations.add(AppointmentNetworkUpdateMigration())
    
    
//    app.migrations.add(WellupContentMigration())
//    app.migrations.add(AttachmentUpdateWellupContentMigration())
//    app.migrations.add(TagUpdateWellupContentMigration())
//    app.migrations.add(OrganizationMetaUpdateMigration())
    
    
    app.migrations.add(ChatMemberMigration())
    app.migrations.add(MemberChatMigration())
    app.migrations.add(ChatMemberChatsMigration())
    app.migrations.add(EmrollmentStatusMigrationUpdate())
    app.migrations.add(TemplateScoreUpdateMigration())
    
    
    app.migrations.add(UserMetaMigrationUpdate())
    app.migrations.add(LastContactMigrationUpdate())
    app.migrations.add(NetworkCarriersMigration())
    app.migrations.add(TimelineItemUpdateMigration())
    app.migrations.add(DeviceUpdateMigration())
    
    
    app.migrations.add(MemberStatusMigration())
    app.migrations.add(AddressMemberStatusUpdateMigration())
    app.migrations.add(MetaMigrationUpdate())
    
//    app.migrations.add(NoteMigrationUpdate())
//    app.migrations.add(NoteUpdateStatus())
//    app.migrations.add(ChatUpdateMigration())
        
    
//    app.migrations.add(DeviceSubIdUpdateMigration())
    
    
    app.migrations.add(NetworkUpdateSchedulerMigration())

    // Associated Person migration - moved here to run before CarePlan migrations
    app.migrations.add(AssociatedPersonMigration())

    app.migrations.add(CreateCarePlan())
    app.migrations.add(CreateGoal())
    app.migrations.add(CreateIntervention())
    app.migrations.add(AddTitleAndResponsiblePartyIdToIntervention())
    app.migrations.add(AddNoteToIntervention())
    app.migrations.add(CreateProblem())
    app.migrations.add(CreateCareTeamMember())
    app.migrations.add(CreateCarePlanReview())
    app.migrations.add(CreateCarePlanFollowUp())
    app.migrations.add(CreateCarePlanService())
    app.migrations.add(AddRefIdToCarePlanService())
    app.migrations.add(AddTitleToCarePlanReview())

    // Add care_plan_id to notes table
    app.migrations.add(AddCarePlanToNotes())

    // In configure.swift
    app.migrations.add(CreateProblemUpdate())
    app.migrations.add(AddTitleToProblem())

    // Add title fields to CarePlan and Goal models
    app.migrations.add(AddTitleToCarePlan())
    app.migrations.add(AddTitleToGoal())

    app.migrations.add(CreateDiagnosis())
    app.migrations.add(CreateMedication())
    app.migrations.add(CreateMemberProblems())
    app.migrations.add(MakeCarePlanIdOptionalInProblems())
    app.migrations.add(TimelineItemCarePlanMigration())

    // Program migrations
    app.migrations.add(CreateProgram())
    app.migrations.add(CreateReviewPeriod())
    app.migrations.add(CreateProgramTask())
    app.migrations.add(MoveOutcomeFieldsToReviewPeriod())
    app.migrations.add(RemoveProgramIdField())
    app.migrations.add(TimelineItemProgramMigration())
    app.migrations.add(FixTimelineItemProgramConstraintMigration())
    
    
    app.queues.add(APNSJob())
    app.queues.add(ReportTestJob())
    
    
    app.views.use(.leaf)
    
    if !isProduction {
        app.logger.logLevel = .debug
    }
    
    
    // register routes
    try routes(app)
    try app.autoMigrate().wait()
}
